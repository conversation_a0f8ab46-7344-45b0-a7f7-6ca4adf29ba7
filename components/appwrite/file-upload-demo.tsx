'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Image, 
  Video, 
  Music, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle,
  Download,
  Eye
} from 'lucide-react';
import { 
  useAppwriteUpload, 
  useImageUpload, 
  useVideoUpload, 
  useAudioUpload, 
  useDocumentUpload 
} from '@/hooks/use-appwrite-upload';
import { FileUpload } from '@/lib/appwrite';

interface FileUploadDemoProps {
  onUploadComplete?: (files: FileUpload[]) => void;
  maxFiles?: number;
  className?: string;
}

export const FileUploadDemo: React.FC<FileUploadDemoProps> = ({
  onUploadComplete,
  maxFiles = 10,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('single');
  const [uploadedFiles, setUploadedFiles] = useState<FileUpload[]>([]);

  // Single file upload hook
  const singleUpload = useAppwriteUpload({
    onSuccess: (result) => {
      if ('id' in result) {
        const newFiles = [...uploadedFiles, result];
        setUploadedFiles(newFiles);
        onUploadComplete?.(newFiles);
      }
    },
    onError: (error) => {
      console.error('Upload error:', error);
    },
    autoReset: true
  });

  // Batch upload hook
  const batchUpload = useAppwriteUpload({
    onSuccess: (result) => {
      if ('successful' in result) {
        const newFiles = [...uploadedFiles, ...result.successful];
        setUploadedFiles(newFiles);
        onUploadComplete?.(newFiles);
      }
    },
    maxFileSize: 100 * 1024 * 1024, // 100MB
    autoReset: true
  });

  // Specialized upload hooks
  const imageUpload = useImageUpload({
    onSuccess: (result) => {
      if ('id' in result) {
        const newFiles = [...uploadedFiles, result];
        setUploadedFiles(newFiles);
        onUploadComplete?.(newFiles);
      }
    },
    autoReset: true
  });

  const videoUpload = useVideoUpload({
    onSuccess: (result) => {
      if ('id' in result) {
        const newFiles = [...uploadedFiles, result];
        setUploadedFiles(newFiles);
        onUploadComplete?.(newFiles);
      }
    },
    autoReset: true
  });

  const audioUpload = useAudioUpload({
    onSuccess: (result) => {
      if ('id' in result) {
        const newFiles = [...uploadedFiles, result];
        setUploadedFiles(newFiles);
        onUploadComplete?.(newFiles);
      }
    },
    autoReset: true
  });

  const documentUpload = useDocumentUpload({
    onSuccess: (result) => {
      if ('id' in result) {
        const newFiles = [...uploadedFiles, result];
        setUploadedFiles(newFiles);
        onUploadComplete?.(newFiles);
      }
    },
    autoReset: true
  });

  // Single file dropzone
  const singleDropzone = useDropzone({
    onDrop: useCallback((acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        singleUpload.upload(acceptedFiles[0], {
          generateThumbnail: true,
          metadata: {
            uploadedAt: new Date().toISOString(),
            source: 'demo-component'
          }
        });
      }
    }, [singleUpload]),
    maxFiles: 1,
    disabled: singleUpload.isUploading
  });

  // Batch dropzone
  const batchDropzone = useDropzone({
    onDrop: useCallback((acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        batchUpload.uploadBatch(acceptedFiles, {
          maxConcurrent: 3,
          generateThumbnail: true,
          metadata: {
            uploadedAt: new Date().toISOString(),
            source: 'demo-batch'
          }
        });
      }
    }, [batchUpload]),
    maxFiles,
    disabled: batchUpload.isUploading
  });

  // Specialized dropzones
  const imageDropzone = useDropzone({
    onDrop: useCallback((acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        imageUpload.uploadImage(acceptedFiles[0], {
          generateThumbnails: true,
          thumbnailSizes: [
            { width: 150, height: 150, suffix: 'thumb' },
            { width: 300, height: 300, suffix: 'medium' },
            { width: 800, height: 600, suffix: 'large' }
          ]
        });
      }
    }, [imageUpload]),
    accept: { 'image/*': [] },
    maxFiles: 1,
    disabled: imageUpload.isUploading
  });

  const videoDropzone = useDropzone({
    onDrop: useCallback((acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        videoUpload.uploadVideo(acceptedFiles[0], {
          extractThumbnail: true,
          thumbnailTimestamp: 5
        });
      }
    }, [videoUpload]),
    accept: { 'video/*': [] },
    maxFiles: 1,
    disabled: videoUpload.isUploading
  });

  const audioDropzone = useDropzone({
    onDrop: useCallback((acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        audioUpload.uploadAudio(acceptedFiles[0], {
          title: acceptedFiles[0].name,
          metadata: {
            uploadType: 'audio-demo'
          }
        });
      }
    }, [audioUpload]),
    accept: { 'audio/*': [] },
    maxFiles: 1,
    disabled: audioUpload.isUploading
  });

  const documentDropzone = useDropzone({
    onDrop: useCallback((acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        documentUpload.uploadDocument(acceptedFiles[0], {
          extractText: true,
          documentType: 'other'
        });
      }
    }, [documentUpload]),
    accept: { 
      'application/pdf': [],
      'application/msword': [],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [],
      'text/plain': []
    },
    maxFiles: 1,
    disabled: documentUpload.isUploading
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (contentType.startsWith('video/')) return <Video className="w-4 h-4" />;
    if (contentType.startsWith('audio/')) return <Music className="w-4 h-4" />;
    return <FileText className="w-4 h-4" />;
  };

  const DropzoneArea = ({ 
    dropzone, 
    isUploading, 
    progress, 
    error, 
    title, 
    description, 
    icon 
  }: {
    dropzone: any;
    isUploading: boolean;
    progress: any;
    error: Error | null;
    title: string;
    description: string;
    icon: React.ReactNode;
  }) => (
    <div
      {...dropzone.getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
        ${dropzone.isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
        ${isUploading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-primary/5'}
      `}
    >
      <input {...dropzone.getInputProps()} />
      <div className="flex flex-col items-center gap-4">
        {icon}
        <div>
          <h3 className="font-semibold">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        
        {isUploading && progress && (
          <div className="w-full max-w-xs">
            <Progress value={progress.progress} className="w-full" />
            <p className="text-xs text-muted-foreground mt-1">
              {progress.filename} - {progress.progress}%
            </p>
          </div>
        )}
        
        {error && (
          <Alert className="max-w-xs">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {error.message}
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Appwrite File Upload Demo
          </CardTitle>
          <CardDescription>
            Demonstrate various file upload capabilities with progress tracking and error handling.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="single">Single</TabsTrigger>
              <TabsTrigger value="batch">Batch</TabsTrigger>
              <TabsTrigger value="image">Images</TabsTrigger>
              <TabsTrigger value="video">Videos</TabsTrigger>
              <TabsTrigger value="audio">Audio</TabsTrigger>
            </TabsList>

            <TabsContent value="single" className="space-y-4">
              <DropzoneArea
                dropzone={singleDropzone}
                isUploading={singleUpload.isUploading}
                progress={singleUpload.progress}
                error={singleUpload.error}
                title="Upload Single File"
                description="Drop any file here or click to browse"
                icon={<Upload className="w-8 h-8 text-muted-foreground" />}
              />
            </TabsContent>

            <TabsContent value="batch" className="space-y-4">
              <DropzoneArea
                dropzone={batchDropzone}
                isUploading={batchUpload.isUploading}
                progress={batchUpload.progress}
                error={batchUpload.error}
                title="Upload Multiple Files"
                description={`Drop up to ${maxFiles} files here or click to browse`}
                icon={<Upload className="w-8 h-8 text-muted-foreground" />}
              />
            </TabsContent>

            <TabsContent value="image" className="space-y-4">
              <DropzoneArea
                dropzone={imageDropzone}
                isUploading={imageUpload.isUploading}
                progress={imageUpload.progress}
                error={imageUpload.error}
                title="Upload Images"
                description="Drop image files here (JPEG, PNG, GIF, WebP)"
                icon={<Image className="w-8 h-8 text-muted-foreground" />}
              />
            </TabsContent>

            <TabsContent value="video" className="space-y-4">
              <DropzoneArea
                dropzone={videoDropzone}
                isUploading={videoUpload.isUploading}
                progress={videoUpload.progress}
                error={videoUpload.error}
                title="Upload Videos"
                description="Drop video files here (MP4, WebM, MOV)"
                icon={<Video className="w-8 h-8 text-muted-foreground" />}
              />
            </TabsContent>

            <TabsContent value="audio" className="space-y-4">
              <DropzoneArea
                dropzone={audioDropzone}
                isUploading={audioUpload.isUploading}
                progress={audioUpload.progress}
                error={audioUpload.error}
                title="Upload Audio"
                description="Drop audio files here (MP3, WAV, OGG)"
                icon={<Music className="w-8 h-8 text-muted-foreground" />}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              Uploaded Files ({uploadedFiles.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getFileIcon(file.contentType)}
                    <div>
                      <p className="font-medium">{file.filename}</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Badge variant="secondary">{file.contentType}</Badge>
                        <span>{formatFileSize(file.size)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(file.url, '_blank')}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(file.downloadUrl, '_blank')}
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FileUploadDemo;
