/**
 * Test file for Appwrite Storage Library
 * This file contains test functions to validate the Appwrite library functionality
 * Run these tests to ensure your Appwrite setup is working correctly
 */

import { 
  AppwriteStorage,
  uploadFile,
  uploadFiles,
  uploadContent,
  uploadImage,
  uploadVideo,
  uploadAudio,
  uploadDocument,
  deleteFile,
  getFile,
  listFiles,
  AppwriteUtils
} from './index';

// Mock file creation for testing
function createMockFile(name: string, type: string, size: number = 1024): File {
  const content = new Array(size).fill('a').join('');
  const blob = new Blob([content], { type });
  return new File([blob], name, { type });
}

// Test configuration
const testConfig = {
  endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || 'test-project',
  bucketId: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_ID || 'test-bucket',
  maxFileSize: 10 * 1024 * 1024, // 10MB
};

export class AppwriteTestSuite {
  private storage: AppwriteStorage;
  private testResults: Array<{ test: string; passed: boolean; error?: string }> = [];

  constructor() {
    this.storage = new AppwriteStorage(testConfig);
  }

  private logResult(testName: string, passed: boolean, error?: string) {
    this.testResults.push({ test: testName, passed, error });
    console.log(`${passed ? '✅' : '❌'} ${testName}${error ? `: ${error}` : ''}`);
  }

  // Test 1: Configuration and initialization
  async testConfiguration() {
    try {
      const config = this.storage.getConfig();
      const isValid = config.endpoint && config.projectId && config.bucketId;
      this.logResult('Configuration Test', isValid);
      return isValid;
    } catch (error) {
      this.logResult('Configuration Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 2: File validation
  async testFileValidation() {
    try {
      // Test valid file
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024);
      const validResult = AppwriteUtils.validateFile(validFile, testConfig);
      
      // Test oversized file
      const oversizedFile = createMockFile('large.jpg', 'image/jpeg', 20 * 1024 * 1024);
      const oversizedResult = AppwriteUtils.validateFile(oversizedFile, testConfig);
      
      const passed = validResult.isValid && !oversizedResult.isValid;
      this.logResult('File Validation Test', passed);
      return passed;
    } catch (error) {
      this.logResult('File Validation Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 3: Single file upload
  async testSingleFileUpload() {
    try {
      const testFile = createMockFile('test-upload.txt', 'text/plain', 1024);
      
      let progressCalled = false;
      let completeCalled = false;
      
      const result = await uploadFile(testFile, {
        onProgress: (progress) => {
          progressCalled = true;
          console.log(`Upload progress: ${progress.progress}%`);
        },
        onComplete: (result) => {
          completeCalled = true;
          console.log('Upload completed:', result.filename);
        },
        metadata: {
          testFile: true,
          uploadedAt: new Date().toISOString()
        }
      });

      const passed = !!(result && result.id && result.url && progressCalled && completeCalled);
      this.logResult('Single File Upload Test', passed);
      
      // Clean up
      if (result?.id) {
        await deleteFile(result.id);
      }
      
      return passed;
    } catch (error) {
      this.logResult('Single File Upload Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 4: Batch file upload
  async testBatchFileUpload() {
    try {
      const testFiles = [
        createMockFile('batch-1.txt', 'text/plain', 512),
        createMockFile('batch-2.txt', 'text/plain', 512),
        createMockFile('batch-3.txt', 'text/plain', 512)
      ];
      
      let batchProgressCalled = false;
      let batchCompleteCalled = false;
      
      const result = await uploadFiles(testFiles, {
        maxConcurrent: 2,
        onBatchProgress: (completed, total) => {
          batchProgressCalled = true;
          console.log(`Batch progress: ${completed}/${total}`);
        },
        onBatchComplete: (result) => {
          batchCompleteCalled = true;
          console.log(`Batch completed: ${result.successCount} successful, ${result.failureCount} failed`);
        }
      });

      const passed = !!(
        result && 
        result.successCount === 3 && 
        result.failureCount === 0 &&
        batchProgressCalled &&
        batchCompleteCalled
      );
      
      this.logResult('Batch File Upload Test', passed);
      
      // Clean up
      for (const file of result.successful) {
        await deleteFile(file.id);
      }
      
      return passed;
    } catch (error) {
      this.logResult('Batch File Upload Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 5: Image upload with thumbnails
  async testImageUpload() {
    try {
      const imageFile = createMockFile('test-image.jpg', 'image/jpeg', 2048);
      
      const result = await uploadImage(imageFile, {
        generateThumbnails: true,
        thumbnailSizes: [
          { width: 150, height: 150, suffix: 'thumb' },
          { width: 300, height: 300, suffix: 'medium' }
        ],
        metadata: {
          alt: 'Test image',
          category: 'test'
        }
      });

      const passed = !!(result && result.id && result.thumbnails);
      this.logResult('Image Upload Test', passed);
      
      // Clean up
      if (result?.id) {
        await deleteFile(result.id);
      }
      
      return passed;
    } catch (error) {
      this.logResult('Image Upload Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 6: Content upload with metadata
  async testContentUpload() {
    try {
      const contentFile = createMockFile('content.pdf', 'application/pdf', 1024);
      
      const result = await uploadContent(contentFile, {
        contentType: 'document',
        title: 'Test Document',
        description: 'A test PDF document',
        tags: ['test', 'document', 'pdf'],
        isPublic: false,
        metadata: {
          author: 'Test Suite',
          version: '1.0'
        }
      });

      const passed = !!(result && result.id && result.contentType === 'document');
      this.logResult('Content Upload Test', passed);
      
      // Clean up
      if (result?.id) {
        await deleteFile(result.id);
      }
      
      return passed;
    } catch (error) {
      this.logResult('Content Upload Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 7: File management operations
  async testFileManagement() {
    try {
      // Upload a test file
      const testFile = createMockFile('management-test.txt', 'text/plain', 512);
      const uploadResult = await uploadFile(testFile);
      
      if (!uploadResult?.id) {
        throw new Error('Failed to upload test file');
      }

      // Test get file
      const fileInfo = await getFile(uploadResult.id);
      const getFilePassed = fileInfo && fileInfo.$id === uploadResult.id;
      
      // Test list files
      const fileList = await listFiles({ limit: 10 });
      const listFilesPassed = fileList && Array.isArray(fileList.files);
      
      // Test delete file
      await deleteFile(uploadResult.id);
      
      // Verify deletion by trying to get the file (should fail)
      let deletePassed = false;
      try {
        await getFile(uploadResult.id);
      } catch (error) {
        deletePassed = true; // Expected to fail
      }

      const passed = getFilePassed && listFilesPassed && deletePassed;
      this.logResult('File Management Test', passed);
      return passed;
    } catch (error) {
      this.logResult('File Management Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 8: URL generation
  async testUrlGeneration() {
    try {
      const testFile = createMockFile('url-test.jpg', 'image/jpeg', 1024);
      const uploadResult = await uploadFile(testFile);
      
      if (!uploadResult?.id) {
        throw new Error('Failed to upload test file');
      }

      // Test URL generation
      const viewUrl = this.storage.getFileViewUrl(uploadResult.id);
      const downloadUrl = this.storage.getFileDownloadUrl(uploadResult.id);
      const previewUrl = this.storage.getImagePreviewUrl(uploadResult.id, 200, 200);
      
      const passed = !!(viewUrl && downloadUrl && previewUrl);
      this.logResult('URL Generation Test', passed);
      
      // Clean up
      await deleteFile(uploadResult.id);
      
      return passed;
    } catch (error) {
      this.logResult('URL Generation Test', false, (error as Error).message);
      return false;
    }
  }

  // Test 9: Error handling
  async testErrorHandling() {
    try {
      // Test with invalid file type (if restrictions are set)
      const invalidFile = createMockFile('test.exe', 'application/x-executable', 1024);
      
      let errorCaught = false;
      try {
        await uploadFile(invalidFile);
      } catch (error) {
        errorCaught = true;
      }

      // Test with non-existent file deletion
      let deleteErrorCaught = false;
      try {
        await deleteFile('non-existent-file-id');
      } catch (error) {
        deleteErrorCaught = true;
      }

      const passed = errorCaught && deleteErrorCaught;
      this.logResult('Error Handling Test', passed);
      return passed;
    } catch (error) {
      this.logResult('Error Handling Test', false, (error as Error).message);
      return false;
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Appwrite Storage Library Test Suite...\n');
    
    const tests = [
      () => this.testConfiguration(),
      () => this.testFileValidation(),
      () => this.testSingleFileUpload(),
      () => this.testBatchFileUpload(),
      () => this.testImageUpload(),
      () => this.testContentUpload(),
      () => this.testFileManagement(),
      () => this.testUrlGeneration(),
      () => this.testErrorHandling()
    ];

    let passedCount = 0;
    
    for (const test of tests) {
      try {
        const result = await test();
        if (result) passedCount++;
      } catch (error) {
        console.error('Test execution error:', error);
      }
    }

    console.log('\n📊 Test Results Summary:');
    console.log(`✅ Passed: ${passedCount}/${tests.length}`);
    console.log(`❌ Failed: ${tests.length - passedCount}/${tests.length}`);
    
    if (passedCount === tests.length) {
      console.log('🎉 All tests passed! Your Appwrite setup is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check your Appwrite configuration and setup.');
    }

    return {
      total: tests.length,
      passed: passedCount,
      failed: tests.length - passedCount,
      results: this.testResults
    };
  }
}

// Export test runner function
export async function runAppwriteTests() {
  const testSuite = new AppwriteTestSuite();
  return await testSuite.runAllTests();
}

// Export individual test functions for selective testing
export {
  createMockFile,
  testConfig
};
