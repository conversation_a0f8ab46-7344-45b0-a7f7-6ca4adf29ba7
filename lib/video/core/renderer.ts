import { EventEmitter } from 'events';
import { 
  VideoSystemConfig, 
  Timeline, 
  RenderSettings, 
  VideoClip,
  Effect,
  Transition 
} from '../types/video-types';

/**
 * Video Renderer
 * Handles final video rendering using WebCodecs and FFmpeg
 */
export class VideoRenderer extends EventEmitter {
  private config: VideoSystemConfig;
  private isRendering: boolean = false;
  private renderProgress: number = 0;
  private videoEncoder: VideoEncoder | null = null;
  private audioEncoder: AudioEncoder | null = null;
  private muxer: any = null; // Would use a proper muxer library

  constructor(config: VideoSystemConfig) {
    super();
    this.config = config;
    this.initializeEncoders();
  }

  private async initializeEncoders(): Promise<void> {
    if (!this.config.webcodecs?.enabled || typeof VideoEncoder === 'undefined') {
      console.warn('WebCodecs not available for rendering');
      return;
    }

    try {
      // Initialize Video Encoder
      this.videoEncoder = new VideoEncoder({
        output: (chunk: EncodedVideoChunk, metadata?: EncodedVideoChunkMetadata) => {
          this.handleEncodedVideoChunk(chunk, metadata);
        },
        error: (error: Error) => {
          console.error('VideoEncoder error:', error);
          this.emit('error', error);
        }
      });

      // Initialize Audio Encoder
      this.audioEncoder = new AudioEncoder({
        output: (chunk: EncodedAudioChunk, metadata?: EncodedAudioChunkMetadata) => {
          this.handleEncodedAudioChunk(chunk, metadata);
        },
        error: (error: Error) => {
          console.error('AudioEncoder error:', error);
          this.emit('error', error);
        }
      });

      this.emit('encoders:initialized');
    } catch (error) {
      console.error('Failed to initialize encoders:', error);
      this.emit('error', error);
    }
  }

  async render(
    timeline: Timeline, 
    settings: RenderSettings,
    progressCallback?: (progress: number) => void
  ): Promise<string> {
    if (this.isRendering) {
      throw new Error('Rendering already in progress');
    }

    this.isRendering = true;
    this.renderProgress = 0;

    try {
      this.emit('render:started', { timeline, settings });

      // Configure encoders
      await this.configureEncoders(settings);

      // Initialize muxer for final output
      await this.initializeMuxer(settings);

      // Render timeline
      const outputPath = await this.renderTimeline(timeline, settings, progressCallback);

      this.emit('render:completed', { outputPath });
      return outputPath;

    } catch (error) {
      this.emit('render:failed', { error });
      throw error;
    } finally {
      this.isRendering = false;
      this.renderProgress = 0;
      await this.cleanup();
    }
  }

  private async configureEncoders(settings: RenderSettings): Promise<void> {
    if (!this.videoEncoder || !this.audioEncoder) {
      throw new Error('Encoders not initialized');
    }

    // Configure Video Encoder
    const videoConfig: VideoEncoderConfig = {
      codec: this.getVideoCodecString(settings.codec),
      width: settings.resolution.width,
      height: settings.resolution.height,
      bitrate: settings.bitrate,
      framerate: settings.fps,
      hardwareAcceleration: settings.hardwareAcceleration ? 'prefer-hardware' : 'prefer-software',
    };

    await this.videoEncoder.configure(videoConfig);

    // Configure Audio Encoder
    const audioConfig: AudioEncoderConfig = {
      codec: 'mp4a.40.2', // AAC
      sampleRate: 48000,
      numberOfChannels: 2,
      bitrate: 128000,
    };

    await this.audioEncoder.configure(audioConfig);
  }

  private async initializeMuxer(settings: RenderSettings): Promise<void> {
    // Initialize muxer based on output format
    // This would typically use a library like MP4Box.js or similar
    console.log('Initializing muxer for format:', settings.format);
  }

  private async renderTimeline(
    timeline: Timeline, 
    settings: RenderSettings,
    progressCallback?: (progress: number) => void
  ): Promise<string> {
    const totalFrames = Math.ceil(timeline.duration * settings.fps);
    const frameTime = 1 / settings.fps;
    
    // Create canvas for compositing
    const canvas = new OffscreenCanvas(settings.resolution.width, settings.resolution.height);
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }

    // Render each frame
    for (let frameIndex = 0; frameIndex < totalFrames; frameIndex++) {
      const currentTime = frameIndex * frameTime;
      
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Get clips active at current time
      const activeClips = timeline.clips.filter(clip => 
        currentTime >= clip.position && currentTime < clip.position + clip.duration
      );

      // Sort clips by track index (lower tracks render first)
      activeClips.sort((a, b) => a.track - b.track);

      // Render each active clip
      for (const clip of activeClips) {
        await this.renderClip(ctx, clip, currentTime, settings);
      }

      // Create VideoFrame from canvas
      const videoFrame = new VideoFrame(canvas, {
        timestamp: frameIndex * (1000000 / settings.fps), // microseconds
      });

      // Encode frame
      if (this.videoEncoder) {
        this.videoEncoder.encode(videoFrame);
      }

      videoFrame.close();

      // Update progress
      this.renderProgress = (frameIndex / totalFrames) * 100;
      if (progressCallback) {
        progressCallback(this.renderProgress);
      }

      this.emit('render:progress', { progress: this.renderProgress });
    }

    // Flush encoders
    if (this.videoEncoder) {
      await this.videoEncoder.flush();
    }
    if (this.audioEncoder) {
      await this.audioEncoder.flush();
    }

    // Finalize output
    const outputPath = await this.finalizeMuxing(settings);
    return outputPath;
  }

  private async renderClip(
    ctx: OffscreenCanvasRenderingContext2D,
    clip: VideoClip,
    currentTime: number,
    settings: RenderSettings
  ): Promise<void> {
    const clipLocalTime = currentTime - clip.position;
    const sourceTime = clip.startTime + clipLocalTime;

    try {
      // Load and render clip content based on type
      switch (clip.type) {
        case 'video':
          await this.renderVideoClip(ctx, clip, sourceTime, settings);
          break;
        case 'image':
          await this.renderImageClip(ctx, clip, settings);
          break;
        case 'text':
          await this.renderTextClip(ctx, clip, settings);
          break;
        case 'ai-generated':
          await this.renderAIGeneratedClip(ctx, clip, sourceTime, settings);
          break;
      }

      // Apply effects
      if (clip.effects && clip.effects.length > 0) {
        await this.applyEffects(ctx, clip.effects, clipLocalTime, settings);
      }

    } catch (error) {
      console.error('Failed to render clip:', clip.id, error);
    }
  }

  private async renderVideoClip(
    ctx: OffscreenCanvasRenderingContext2D,
    clip: VideoClip,
    sourceTime: number,
    settings: RenderSettings
  ): Promise<void> {
    // Create video element for the clip
    const video = document.createElement('video');
    video.src = typeof clip.source === 'string' ? clip.source : URL.createObjectURL(clip.source as Blob);
    video.currentTime = sourceTime;

    return new Promise((resolve) => {
      video.addEventListener('seeked', () => {
        ctx.drawImage(video, 0, 0, settings.resolution.width, settings.resolution.height);
        resolve();
      }, { once: true });
    });
  }

  private async renderImageClip(
    ctx: OffscreenCanvasRenderingContext2D,
    clip: VideoClip,
    settings: RenderSettings
  ): Promise<void> {
    const img = new Image();
    img.src = typeof clip.source === 'string' ? clip.source : URL.createObjectURL(clip.source as Blob);

    return new Promise((resolve, reject) => {
      img.onload = () => {
        ctx.drawImage(img, 0, 0, settings.resolution.width, settings.resolution.height);
        resolve();
      };
      img.onerror = reject;
    });
  }

  private async renderTextClip(
    ctx: OffscreenCanvasRenderingContext2D,
    clip: VideoClip,
    settings: RenderSettings
  ): Promise<void> {
    const text = typeof clip.source === 'string' ? clip.source : 'Text';
    
    // Apply text styling (would be configurable)
    ctx.font = '48px Arial';
    ctx.fillStyle = 'white';
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 2;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const x = settings.resolution.width / 2;
    const y = settings.resolution.height / 2;

    ctx.strokeText(text, x, y);
    ctx.fillText(text, x, y);
  }

  private async renderAIGeneratedClip(
    ctx: OffscreenCanvasRenderingContext2D,
    clip: VideoClip,
    sourceTime: number,
    settings: RenderSettings
  ): Promise<void> {
    // Handle AI-generated content similar to video clips
    await this.renderVideoClip(ctx, clip, sourceTime, settings);
  }

  private async applyEffects(
    ctx: OffscreenCanvasRenderingContext2D,
    effects: Effect[],
    clipTime: number,
    settings: RenderSettings
  ): Promise<void> {
    for (const effect of effects) {
      if (!effect.enabled || clipTime < effect.startTime || clipTime > effect.endTime) {
        continue;
      }

      await this.applyEffect(ctx, effect, clipTime, settings);
    }
  }

  private async applyEffect(
    ctx: OffscreenCanvasRenderingContext2D,
    effect: Effect,
    clipTime: number,
    settings: RenderSettings
  ): Promise<void> {
    const canvas = ctx.canvas as OffscreenCanvas;
    
    switch (effect.type) {
      case 'blur':
        ctx.filter = `blur(${effect.parameters.radius || 5}px)`;
        break;
      case 'brightness':
        ctx.filter = `brightness(${effect.parameters.value || 1})`;
        break;
      case 'contrast':
        ctx.filter = `contrast(${effect.parameters.value || 1})`;
        break;
      case 'saturation':
        ctx.filter = `saturate(${effect.parameters.value || 1})`;
        break;
      case 'opacity':
        ctx.globalAlpha = effect.parameters.value || 1;
        break;
      case 'scale':
        const scale = effect.parameters.value || 1;
        ctx.scale(scale, scale);
        break;
      case 'rotate':
        const angle = (effect.parameters.degrees || 0) * Math.PI / 180;
        ctx.rotate(angle);
        break;
      // Add more effects as needed
    }
  }

  private handleEncodedVideoChunk(chunk: EncodedVideoChunk, metadata?: EncodedVideoChunkMetadata): void {
    // Send encoded video chunk to muxer
    if (this.muxer) {
      this.muxer.addVideoChunk(chunk, metadata);
    }
  }

  private handleEncodedAudioChunk(chunk: EncodedAudioChunk, metadata?: EncodedAudioChunkMetadata): void {
    // Send encoded audio chunk to muxer
    if (this.muxer) {
      this.muxer.addAudioChunk(chunk, metadata);
    }
  }

  private async finalizeMuxing(settings: RenderSettings): Promise<string> {
    // Finalize the muxed output file
    const outputPath = `${this.config.tempDirectory}/output-${Date.now()}.${settings.format}`;
    
    if (this.muxer) {
      await this.muxer.finalize(outputPath);
    }

    return outputPath;
  }

  private getVideoCodecString(codec: string): string {
    const codecMap: Record<string, string> = {
      'h264': 'avc1.42E01E',
      'h265': 'hev1.1.6.L93.B0',
      'vp8': 'vp8',
      'vp9': 'vp09.00.10.08',
      'av1': 'av01.0.04M.08',
    };

    return codecMap[codec] || codecMap['h264'];
  }

  // Export specific clips or ranges
  async exportClip(clip: VideoClip, settings: RenderSettings): Promise<string> {
    // Create a temporary timeline with just this clip
    const tempTimeline: Timeline = {
      id: 'temp-export',
      name: 'Export',
      duration: clip.duration,
      fps: settings.fps,
      resolution: settings.resolution,
      tracks: [
        {
          id: 'temp-track',
          type: clip.type === 'audio' ? 'audio' : 'video',
          name: 'Export Track',
          index: 0,
          enabled: true,
          locked: false,
        }
      ],
      clips: [{ ...clip, position: 0, track: 0 }],
      createdAt: new Date(),
      modifiedAt: new Date(),
    };

    return this.render(tempTimeline, settings);
  }

  async exportRange(
    timeline: Timeline, 
    startTime: number, 
    endTime: number, 
    settings: RenderSettings
  ): Promise<string> {
    // Filter clips to the specified range
    const rangeClips = timeline.clips
      .filter(clip => !(clip.position + clip.duration <= startTime || clip.position >= endTime))
      .map(clip => {
        const newClip = { ...clip };
        
        // Adjust clip timing for the range
        if (clip.position < startTime) {
          const offset = startTime - clip.position;
          newClip.startTime += offset;
          newClip.duration -= offset;
          newClip.position = 0;
        } else {
          newClip.position -= startTime;
        }

        if (newClip.position + newClip.duration > endTime - startTime) {
          newClip.duration = (endTime - startTime) - newClip.position;
          newClip.endTime = newClip.startTime + newClip.duration;
        }

        return newClip;
      });

    const rangeTimeline: Timeline = {
      ...timeline,
      id: 'range-export',
      duration: endTime - startTime,
      clips: rangeClips,
    };

    return this.render(rangeTimeline, settings);
  }

  // Utility methods
  /*isRendering(): boolean {
    return this.isRendering;
  }*/

  getRenderProgress(): number {
    return this.renderProgress;
  }

  async cancelRender(): Promise<void> {
    if (!this.isRendering) return;

    this.isRendering = false;
    await this.cleanup();
    this.emit('render:cancelled');
  }

  private async cleanup(): Promise<void> {
    // Clean up encoders
    if (this.videoEncoder && this.videoEncoder.state !== 'closed') {
      this.videoEncoder.close();
    }
    if (this.audioEncoder && this.audioEncoder.state !== 'closed') {
      this.audioEncoder.close();
    }

    // Clean up muxer
    if (this.muxer) {
      await this.muxer.cleanup();
      this.muxer = null;
    }
  }

  // Configuration
  updateConfig(config: VideoSystemConfig): void {
    this.config = { ...this.config, ...config };
  }

  // Disposal
  async dispose(): Promise<void> {
    await this.cancelRender();
    this.removeAllListeners();
  }
}