import { EventEmitter } from 'events';
import { VideoSystemConfig, VideoClip, VideoMetadata, VideoAnalytics } from '../types/video-types';

/**
 * Core Video Engine
 * Handles video playback, processing, and WebCodecs integration
 */
export class VideoEngine extends EventEmitter {
  private config: VideoSystemConfig;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private currentTime: number = 0;
  private duration: number = 0;
  private isPlayingState: boolean = false;
  private animationFrameId: number | null = null;
  private videoDecoder: VideoDecoder | null = null;
  private audioContext: AudioContext | null = null;
  private mediaElements: Map<string, HTMLVideoElement | HTMLAudioElement> = new Map();

  constructor(config: VideoSystemConfig) {
    super();
    this.config = config;
    this.initializeWebCodecs();
  }

  private async initializeWebCodecs(): Promise<void> {
    if (!this.config.webcodecs?.enabled || typeof VideoDecoder === 'undefined') {
      console.warn('WebCodecs not available, falling back to traditional methods');
      return;
    }

    try {
      // Initialize WebCodecs VideoDecoder
      this.videoDecoder = new VideoDecoder({
        output: (frame: VideoFrame) => {
          this.renderFrame(frame);
          frame.close();
        },
        error: (error: Error) => {
          console.error('VideoDecoder error:', error);
          this.emit('error', error);
        }
      });

      // Initialize Web Audio API
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      this.emit('engine:initialized');
    } catch (error) {
      console.error('Failed to initialize WebCodecs:', error);
      this.emit('error', error);
    }
  }

  async initializeCanvas(canvas: HTMLCanvasElement): Promise<void> {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    
    if (!this.ctx) {
      throw new Error('Failed to get 2D context from canvas');
    }

    this.emit('canvas:initialized', { canvas });
  }

  async loadVideo(source: string | File | Blob): Promise<VideoMetadata> {
    try {
      const video = document.createElement('video');
      video.crossOrigin = 'anonymous';
      
      if (typeof source === 'string') {
        video.src = source;
      } else {
        video.src = URL.createObjectURL(source);
      }

      return new Promise((resolve, reject) => {
        video.addEventListener('loadedmetadata', () => {
          const metadata: VideoMetadata = {
            width: video.videoWidth,
            height: video.videoHeight,
            fps: 30, // Default, would need more sophisticated detection
            format: this.getVideoFormat(source),
          };

          this.duration = video.duration;
          this.mediaElements.set('main-video', video);
          
          resolve(metadata);
        });

        video.addEventListener('error', () => {
          reject(new Error('Failed to load video'));
        });

        video.load();
      });
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async loadAudio(source: string | File | Blob): Promise<void> {
    try {
      const audio = document.createElement('audio');
      
      if (typeof source === 'string') {
        audio.src = source;
      } else {
        audio.src = URL.createObjectURL(source);
      }

      return new Promise((resolve, reject) => {
        audio.addEventListener('loadedmetadata', () => {
          this.mediaElements.set('main-audio', audio);
          resolve();
        });

        audio.addEventListener('error', () => {
          reject(new Error('Failed to load audio'));
        });

        audio.load();
      });
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async play(): Promise<void> {
    if (this.isPlayingState) return;

    this.isPlayingState = true;
    
    // Start playback for all media elements
    const playPromises = Array.from(this.mediaElements.values()).map(element => {
      element.currentTime = this.currentTime;
      return element.play().catch(error => {
        console.warn('Failed to play media element:', error);
      });
    });

    await Promise.allSettled(playPromises);
    
    this.startRenderLoop();
    this.emit('playback:started', { currentTime: this.currentTime });
  }

  async pause(): Promise<void> {
    if (!this.isPlayingState) return;

    this.isPlayingState = false;
    
    // Pause all media elements
    this.mediaElements.forEach(element => {
      element.pause();
    });

    this.stopRenderLoop();
    this.emit('playback:paused', { currentTime: this.currentTime });
  }

  async stop(): Promise<void> {
    this.isPlayingState = false;
    this.currentTime = 0;
    
    // Stop and reset all media elements
    this.mediaElements.forEach(element => {
      element.pause();
      element.currentTime = 0;
    });

    this.stopRenderLoop();
    this.clearCanvas();
    this.emit('playback:stopped', { currentTime: 0 });
  }

  async seek(time: number): Promise<void> {
    this.currentTime = Math.max(0, Math.min(time, this.duration));
    
    // Seek all media elements
    this.mediaElements.forEach(element => {
      element.currentTime = this.currentTime;
    });

    if (!this.isPlayingState) {
      this.renderCurrentFrame();
    }

    this.emit('playback:seek', { currentTime: this.currentTime });
  }

  private startRenderLoop(): void {
    if (this.animationFrameId) return;

    const render = () => {
      if (!this.isPlayingState) return;

      this.updateCurrentTime();
      this.renderCurrentFrame();
      
      if (this.currentTime >= this.duration) {
        this.stop();
        this.emit('playback:ended');
        return;
      }

      this.animationFrameId = requestAnimationFrame(render);
    };

    this.animationFrameId = requestAnimationFrame(render);
  }

  private stopRenderLoop(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private updateCurrentTime(): void {
    // Get time from the main video element if available
    const mainVideo = this.mediaElements.get('main-video') as HTMLVideoElement;
    if (mainVideo) {
      this.currentTime = mainVideo.currentTime;
    }
    
    this.emit('playback:timeupdate', { currentTime: this.currentTime });
  }

  private renderCurrentFrame(): void {
    if (!this.canvas || !this.ctx) return;

    this.clearCanvas();

    // Render video frame
    const mainVideo = this.mediaElements.get('main-video') as HTMLVideoElement;
    if (mainVideo && mainVideo.readyState >= 2) {
      this.ctx.drawImage(mainVideo, 0, 0, this.canvas.width, this.canvas.height);
    }

    this.emit('frame:rendered', { currentTime: this.currentTime });
  }

  private renderFrame(frame: VideoFrame): void {
    if (!this.canvas || !this.ctx) {
      frame.close();
      return;
    }

    // Create ImageBitmap from VideoFrame and draw to canvas
    createImageBitmap(frame).then(bitmap => {
      if (this.ctx) {
        this.ctx.drawImage(bitmap, 0, 0, this.canvas!.width, this.canvas!.height);
      }
      bitmap.close();
    }).catch(error => {
      console.error('Failed to render frame:', error);
    });
  }

  private clearCanvas(): void {
    if (!this.canvas || !this.ctx) return;
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
  }

  private getVideoFormat(source: string | File | Blob): string {
    if (typeof source === 'string') {
      const extension = source.split('.').pop()?.toLowerCase();
      return extension || 'unknown';
    } else if (source instanceof File) {
      return source.type.split('/')[1] || 'unknown';
    }
    return 'blob';
  }

  // Analysis and Utilities
  async analyzeVideo(source: string | File | Blob): Promise<VideoAnalytics> {
    const metadata = await this.loadVideo(source);
    
    // Generate thumbnails
    const thumbnails = await this.generateThumbnails(source, 10);
    
    // Generate waveform if audio is present
    const waveform = await this.generateWaveform(source);

    return {
      duration: this.duration,
      resolution: {
        width: metadata.width,
        height: metadata.height,
        aspectRatio: `${metadata.width}:${metadata.height}`,
      },
      fps: metadata.fps,
      bitrate: metadata.bitrate || 0,
      fileSize: metadata.fileSize || 0,
      codec: metadata.codec || 'unknown',
      format: metadata.format || 'unknown',
      hasAudio: true, // Would need proper detection
      thumbnails,
      waveform,
    };
  }

  private async generateThumbnails(source: string | File | Blob, count: number): Promise<string[]> {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return [];

    if (typeof source === 'string') {
      video.src = source;
    } else {
      video.src = URL.createObjectURL(source);
    }

    return new Promise((resolve) => {
      video.addEventListener('loadedmetadata', async () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        const thumbnails: string[] = [];
        const interval = video.duration / count;

        for (let i = 0; i < count; i++) {
          video.currentTime = i * interval;
          
          await new Promise(resolve => {
            video.addEventListener('seeked', resolve, { once: true });
          });

          ctx.drawImage(video, 0, 0);
          thumbnails.push(canvas.toDataURL('image/jpeg', 0.8));
        }

        resolve(thumbnails);
      });

      video.load();
    });
  }

  private async generateWaveform(source: string | File | Blob): Promise<number[]> {
    if (!this.audioContext) return [];

    try {
      let audioBuffer: ArrayBuffer;
      
      if (typeof source === 'string') {
        const response = await fetch(source);
        audioBuffer = await response.arrayBuffer();
      } else {
        audioBuffer = await source.arrayBuffer();
      }

      const decodedAudio = await this.audioContext.decodeAudioData(audioBuffer);
      const channelData = decodedAudio.getChannelData(0);
      
      // Downsample for waveform visualization
      const samples = 1000;
      const blockSize = Math.floor(channelData.length / samples);
      const waveform: number[] = [];

      for (let i = 0; i < samples; i++) {
        let sum = 0;
        for (let j = 0; j < blockSize; j++) {
          sum += Math.abs(channelData[i * blockSize + j]);
        }
        waveform.push(sum / blockSize);
      }

      return waveform;
    } catch (error) {
      console.error('Failed to generate waveform:', error);
      return [];
    }
  }

  // Getters
  getCurrentTime(): number {
    return this.currentTime;
  }

  getDuration(): number {
    return this.duration;
  }

  isPlaying(): boolean {
    return this.isPlayingState;
  }

  getCanvas(): HTMLCanvasElement | null {
    return this.canvas;
  }

  // Configuration
  updateConfig(config: VideoSystemConfig): void {
    this.config = { ...this.config, ...config };
  }

  // Cleanup
  async dispose(): Promise<void> {
    await this.stop();
    
    // Clean up media elements
    this.mediaElements.forEach(element => {
      if (element.src.startsWith('blob:')) {
        URL.revokeObjectURL(element.src);
      }
    });
    this.mediaElements.clear();

    // Clean up WebCodecs
    if (this.videoDecoder) {
      this.videoDecoder.close();
      this.videoDecoder = null;
    }

    // Clean up Audio Context
    if (this.audioContext) {
      await this.audioContext.close();
      this.audioContext = null;
    }

    this.removeAllListeners();
  }
}